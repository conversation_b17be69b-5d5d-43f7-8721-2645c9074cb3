"""
Tests for configuration system.

This module contains comprehensive tests for the configuration management
system including validation, environment variable loading, and dependency injection.
"""

import json
import os
import pytest
from unittest.mock import patch
from pydantic import ValidationError

from app.core.config import Settings, get_settings


class TestSettings:
    """Test the Settings class validation and functionality."""

    def test_settings_with_minimal_config(self):
        """Test Settings with only required configuration."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com"
        }, clear=True):
            settings = Settings()
            
            assert settings.medusa_backend_url == "https://api.example.com"
            assert settings.api_timeout == 30  # default
            assert settings.model_mappings == {}  # default
            assert settings.log_level == "INFO"  # default
            assert settings.debug is False  # default
            assert settings.max_concurrent_requests == 100  # default

    def test_settings_with_full_config(self):
        """Test Settings with all configuration options."""
        model_mappings = {"llama3": "gpt-4o-mini", "codellama": "gpt-4"}
        
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "API_TIMEOUT": "60",
            "MODEL_MAPPINGS": json.dumps(model_mappings),
            "LOG_LEVEL": "DEBUG",
            "APP_NAME": "Test Bridge",
            "DEBUG": "true",
            "MAX_CONCURRENT_REQUESTS": "200"
        }, clear=True):
            settings = Settings()
            
            assert settings.medusa_backend_url == "https://api.example.com"
            assert settings.api_timeout == 60
            assert settings.model_mappings == model_mappings
            assert settings.log_level == "DEBUG"
            assert settings.app_name == "Test Bridge"
            assert settings.debug is True
            assert settings.max_concurrent_requests == 200

    def test_missing_required_field(self):
        """Test that missing required field raises ValidationError."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            
            error = exc_info.value
            assert "medusa_backend_url" in str(error)
            assert "Field required" in str(error)

    def test_api_timeout_validation(self):
        """Test API timeout field validation."""
        # Test minimum value
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "API_TIMEOUT": "0"
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "greater than or equal to 1" in str(exc_info.value)

        # Test maximum value
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "API_TIMEOUT": "301"
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "less than or equal to 300" in str(exc_info.value)

        # Test valid value
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "API_TIMEOUT": "150"
        }, clear=True):
            settings = Settings()
            assert settings.api_timeout == 150

    def test_max_concurrent_requests_validation(self):
        """Test max concurrent requests field validation."""
        # Test minimum value
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MAX_CONCURRENT_REQUESTS": "0"
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "greater than or equal to 1" in str(exc_info.value)

        # Test maximum value
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MAX_CONCURRENT_REQUESTS": "1001"
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "less than or equal to 1000" in str(exc_info.value)


class TestModelMappingsValidation:
    """Test model mappings field validation."""

    def test_model_mappings_json_string(self):
        """Test model mappings from JSON string."""
        model_mappings = {"llama3": "gpt-4o-mini", "codellama": "gpt-4"}
        
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MODEL_MAPPINGS": json.dumps(model_mappings)
        }, clear=True):
            settings = Settings()
            assert settings.model_mappings == model_mappings

    def test_model_mappings_empty_string(self):
        """Test model mappings with empty string."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MODEL_MAPPINGS": ""
        }, clear=True):
            settings = Settings()
            assert settings.model_mappings == {}

    def test_model_mappings_invalid_json(self):
        """Test model mappings with invalid JSON."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MODEL_MAPPINGS": "invalid json"
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "Invalid JSON in model mappings" in str(exc_info.value)

    def test_model_mappings_non_dict_json(self):
        """Test model mappings with non-dict JSON."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MODEL_MAPPINGS": json.dumps(["not", "a", "dict"])
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "Model mappings must be a JSON object" in str(exc_info.value)

    def test_model_mappings_dict_input(self):
        """Test model mappings with direct dict input."""
        # This would happen in testing scenarios
        model_mappings = {"llama3": "gpt-4o-mini"}
        settings = Settings(
            medusa_backend_url="https://api.example.com",
            model_mappings=model_mappings
        )
        assert settings.model_mappings == model_mappings

    def test_model_mappings_type_conversion(self):
        """Test model mappings type conversion to strings."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "MODEL_MAPPINGS": json.dumps({"model1": 123, 456: "model2"})
        }, clear=True):
            settings = Settings()
            assert settings.model_mappings == {"model1": "123", "456": "model2"}


class TestLogLevelValidation:
    """Test log level field validation."""

    @pytest.mark.parametrize("log_level", ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
    def test_valid_log_levels(self, log_level):
        """Test all valid log levels."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "LOG_LEVEL": log_level
        }, clear=True):
            settings = Settings()
            assert settings.log_level == log_level

    @pytest.mark.parametrize("log_level", ["debug", "info", "warning", "error", "critical"])
    def test_case_insensitive_log_levels(self, log_level):
        """Test case insensitive log levels."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "LOG_LEVEL": log_level
        }, clear=True):
            settings = Settings()
            assert settings.log_level == log_level.upper()

    def test_invalid_log_level(self):
        """Test invalid log level."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com",
            "LOG_LEVEL": "INVALID"
        }, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "Log level must be one of" in str(exc_info.value)
            assert "DEBUG, INFO, WARNING, ERROR, CRITICAL" in str(exc_info.value)


class TestGetSettings:
    """Test the get_settings function."""

    def test_get_settings_caching(self):
        """Test that get_settings returns the same instance (caching)."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com"
        }, clear=True):
            # Clear the cache first
            get_settings.cache_clear()
            
            settings1 = get_settings()
            settings2 = get_settings()
            
            # Should be the same instance due to caching
            assert settings1 is settings2

    def test_get_settings_cache_clear(self):
        """Test cache clearing functionality."""
        with patch.dict(os.environ, {
            "MEDUSA_BACKEND_URL": "https://api.example.com"
        }, clear=True):
            get_settings.cache_clear()
            
            settings1 = get_settings()
            get_settings.cache_clear()
            settings2 = get_settings()
            
            # Should be different instances after cache clear
            assert settings1 is not settings2
            # But should have same values
            assert settings1.medusa_backend_url == settings2.medusa_backend_url
